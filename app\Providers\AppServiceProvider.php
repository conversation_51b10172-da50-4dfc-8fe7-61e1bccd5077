<?php

namespace App\Providers;

use Dedo<PERSON>\Scramble\Scramble;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\ServiceProvider;
use Dedoc\Scramble\Support\Generator\OpenApi;
use Dedoc\Scramble\Support\Generator\SecurityScheme;
use Livewire\Component;
use Illuminate\Support\Facades\Gate;
use Illuminate\Pagination\Paginator;
use App\Models\Order;
use App\Models\Product;
use App\Observers\OrderObserver;
use App\Observers\ProductObserver;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Konfigurasi Paginator untuk menggunakan Tailwind
        Paginator::useBootstrap();

        // Register Product Observer
        Product::observe(ProductObserver::class);

        // Register Order Observer
        Order::observe(OrderObserver::class);

        $forceScheme = env('FORCE_SCHEME', 'http');
        URL::forceScheme($forceScheme);

        if ($forceScheme === 'https') {
            request()->server->set('HTTP_X_FORWARDED_PROTO', 'https');
        }

        Scramble::afterOpenApiGenerated(function (OpenApi $openApi) {
            $openApi->secure(
                SecurityScheme::http('bearer')
            );
        });

        // Mengatur akses ke dokumentasi API
        Gate::define('viewApiDocs', function () {
            // Selalu minta password dari env
            return request()->hasHeader('PHP_AUTH_PW') &&
                   request()->header('PHP_AUTH_PW') === env('SCRAMBLE_DOCS_PASSWORD');
        });
    }
}
