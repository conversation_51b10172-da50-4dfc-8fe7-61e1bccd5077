<!DOCTYPE html>
<html>
<head>
    <title>Katalog Produk</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            max-width: 200px;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .product-row:nth-child(even) {
            background-color: #f9f9f9;
        }
        .product-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
        }
    </style>
</head>
<body>
    <div class="header">
        <img src="{{ public_path('images/logo_sinaraartha.png') }}" alt="Logo" class="logo" height="100" width="200">
        <h1>Katalog Produk</h1>
        <p>Daftar Produk per {{ date('d/m/Y') }}</p>
    </div>

    <table>
        <thead>
            <tr>
                <th>Gambar</th>
                <th><PERSON><PERSON>duk</th>
                <th><PERSON><PERSON><PERSON></th>
                <th><PERSON><PERSON></th>
                <th>Stok</th>
            </tr>
        </thead>
        <tbody>
            @foreach($products as $product)
            <tr class="product-row">
                <td>
                    @if($product->image)
                        <img src="{{ public_path('storage/' . $product->image) }}" alt="{{ $product->name }}" class="product-image">
                    @endif
                </td>
                <td>{{ $product->name }}</td>
                <td>{{ $product->category?->name ?? 'Tanpa Kategori' }}</td>
                <td>Rp {{ number_format($product->price, 0, ',', '.') }}</td>
                <td>{{ $product->stock }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>
</body>
</html>
